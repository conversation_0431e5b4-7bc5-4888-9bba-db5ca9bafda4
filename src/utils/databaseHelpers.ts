import { supabase } from '@/lib/supabase'
import { PostgrestError } from '@supabase/supabase-js'

/**
 * Enhanced database query helpers with proper error handling
 */

export interface QueryResult<T = any> {
  success: boolean
  data?: T
  error?: string
  details?: any
}

/**
 * Safe count query that handles RLS and permissions properly
 */
export const safeCount = async (tableName: string): Promise<QueryResult<number>> => {
  try {
    const { count, error } = await supabase
      .from(tableName)
      .select('*', { count: 'exact', head: true })

    if (error) {
      console.error(`Count query failed for ${tableName}:`, error)
      return {
        success: false,
        error: error.message,
        details: error
      }
    }

    return {
      success: true,
      data: count || 0
    }
  } catch (error) {
    console.error(`Unexpected error counting ${tableName}:`, error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: error
    }
  }
}

/**
 * Safe select query with proper error handling
 */
export const safeSelect = async <T = any>(
  tableName: string,
  columns: string = '*',
  options: {
    limit?: number
    orderBy?: { column: string; ascending?: boolean }
    filters?: Record<string, any>
  } = {}
): Promise<QueryResult<T[]>> => {
  try {
    let query = supabase.from(tableName).select(columns)

    // Apply filters
    if (options.filters) {
      Object.entries(options.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          query = query.eq(key, value)
        }
      })
    }

    // Apply ordering
    if (options.orderBy) {
      query = query.order(options.orderBy.column, { 
        ascending: options.orderBy.ascending ?? true 
      })
    }

    // Apply limit
    if (options.limit) {
      query = query.limit(options.limit)
    }

    const { data, error } = await query

    if (error) {
      console.error(`Select query failed for ${tableName}:`, error)
      return {
        success: false,
        error: error.message,
        details: error
      }
    }

    return {
      success: true,
      data: data || []
    }
  } catch (error) {
    console.error(`Unexpected error selecting from ${tableName}:`, error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: error
    }
  }
}

/**
 * Test database connectivity and permissions
 */
export const testDatabaseConnectivity = async (): Promise<QueryResult<{
  profiles: number
  appointments: number
  companies: number
}>> => {
  try {
    console.log('🔍 Testing database connectivity...')

    // Test each table
    const profilesResult = await safeCount('profiles')
    const appointmentsResult = await safeCount('appointments')
    const companiesResult = await safeCount('companies')

    // Check if any failed
    const failures = []
    if (!profilesResult.success) failures.push(`Profiles: ${profilesResult.error}`)
    if (!appointmentsResult.success) failures.push(`Appointments: ${appointmentsResult.error}`)
    if (!companiesResult.success) failures.push(`Companies: ${companiesResult.error}`)

    if (failures.length > 0) {
      return {
        success: false,
        error: `Database connectivity issues: ${failures.join(', ')}`,
        details: { profilesResult, appointmentsResult, companiesResult }
      }
    }

    const counts = {
      profiles: profilesResult.data || 0,
      appointments: appointmentsResult.data || 0,
      companies: companiesResult.data || 0
    }

    console.log('✅ Database connectivity test passed:', counts)

    return {
      success: true,
      data: counts
    }
  } catch (error) {
    console.error('❌ Database connectivity test failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: error
    }
  }
}

/**
 * Enhanced error message parser for Supabase errors
 */
export const parseSupabaseError = (error: PostgrestError | Error): {
  userMessage: string
  technicalMessage: string
  isPermissionError: boolean
  isConnectionError: boolean
} => {
  const errorMessage = error.message || 'Unknown error'
  
  // Check for common error patterns
  const isPermissionError = errorMessage.includes('permission') || 
                           errorMessage.includes('RLS') ||
                           errorMessage.includes('policy') ||
                           errorMessage.includes('access denied')
  
  const isConnectionError = errorMessage.includes('connection') ||
                           errorMessage.includes('network') ||
                           errorMessage.includes('timeout') ||
                           errorMessage.includes('ECONNREFUSED')

  let userMessage = 'An unexpected error occurred'
  
  if (isPermissionError) {
    userMessage = 'You do not have permission to access this data'
  } else if (isConnectionError) {
    userMessage = 'Unable to connect to the database. Please check your connection.'
  } else if (errorMessage.includes('duplicate')) {
    userMessage = 'This record already exists'
  } else if (errorMessage.includes('foreign key')) {
    userMessage = 'This operation would violate data integrity constraints'
  } else if (errorMessage.includes('not found')) {
    userMessage = 'The requested data was not found'
  }

  return {
    userMessage,
    technicalMessage: errorMessage,
    isPermissionError,
    isConnectionError
  }
}

/**
 * Retry mechanism for database operations
 */
export const withRetry = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delayMs: number = 1000
): Promise<T> => {
  let lastError: Error | null = null
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown error')
      
      if (attempt === maxRetries) {
        throw lastError
      }
      
      console.warn(`Operation failed (attempt ${attempt}/${maxRetries}):`, lastError.message)
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delayMs * attempt))
    }
  }
  
  throw lastError
}
