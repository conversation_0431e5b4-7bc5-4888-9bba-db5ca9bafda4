import { supabase } from '@/lib/supabase'
import { AppointmentsService } from '@/services/appointmentsService'

export const testDatabaseConnection = async () => {
  console.log('🔍 Testing database connection...')

  try {
    // Test 1: Basic connection - use proper count query
    const { count: profileCount, error: connectionError } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true })

    if (connectionError) {
      console.error('❌ Connection test failed:', connectionError)
      return { success: false, error: connectionError.message }
    }

    console.log('✅ Basic connection successful, profiles count:', profileCount)

    // Test 2: Authentication
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError) {
      console.error('❌ User authentication failed:', userError)
      return { success: false, error: userError.message }
    }

    console.log('✅ User authenticated:', user?.email)

    // Test 3: Profile access
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user?.id)
      .single()

    if (profileError) {
      console.error('❌ Profile access failed:', profileError)
      return { success: false, error: profileError.message }
    }

    console.log('✅ Profile loaded:', profile?.role, profile?.company_id)

    // Test 4: Appointments access
    const { data: appointments, error: appointmentsError } = await supabase
      .from('appointments')
      .select('*')
      .limit(5)

    if (appointmentsError) {
      console.error('❌ Appointments access failed:', appointmentsError)
      return { success: false, error: appointmentsError.message }
    }

    console.log('✅ Appointments accessible:', appointments?.length, 'found')

    // Test 5: Companies access
    const { data: companies, error: companiesError } = await supabase
      .from('companies')
      .select('*')
      .limit(5)

    if (companiesError) {
      console.error('❌ Companies access failed:', companiesError)
      return { success: false, error: companiesError.message }
    }

    console.log('✅ Companies accessible:', companies?.length, 'found')

    // Test 6: Service layer
    try {
      const serviceAppointments = await AppointmentsService.getAppointments()
      console.log('✅ Service layer working:', serviceAppointments.length, 'appointments')
    } catch (serviceError) {
      console.error('❌ Service layer failed:', serviceError)
      return { 
        success: false, 
        error: `Service layer error: ${serviceError instanceof Error ? serviceError.message : 'Unknown error'}` 
      }
    }

    return {
      success: true,
      message: 'All tests passed',
      details: {
        user: user?.email,
        profile: profile?.role,
        appointmentsCount: appointments?.length,
        companiesCount: companies?.length
      }
    }

  } catch (error) {
    console.error('❌ Unexpected error during testing:', error)
    return {
      success: false,
      error: `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`
    }
  }
}

export const logEnvironmentInfo = () => {
  console.log('🌍 Environment Information:')
  console.log('- NODE_ENV:', process.env.NODE_ENV)
  console.log('- Supabase URL:', import.meta.env.VITE_SUPABASE_URL ? 'Set' : 'Missing')
  console.log('- Supabase Key:', import.meta.env.VITE_SUPABASE_ANON_KEY ? 'Set' : 'Missing')
  console.log('- User Agent:', navigator.userAgent)
  console.log('- Current URL:', window.location.href)
}

export const debugAppointmentsHook = async () => {
  console.log('🔧 Debugging appointments hook...')
  
  try {
    // Test the service directly
    const appointments = await AppointmentsService.getAppointments()
    console.log('📊 Direct service call result:', {
      count: appointments.length,
      sample: appointments[0],
      allAppointments: appointments
    })

    // Test with filters
    const today = new Date()
    const startOfWeek = new Date(today.setDate(today.getDate() - today.getDay()))
    const endOfWeek = new Date(today.setDate(today.getDate() - today.getDay() + 6))
    
    const filteredAppointments = await AppointmentsService.getAppointments({
      dateRange: { start: startOfWeek, end: endOfWeek }
    })
    
    console.log('📊 Filtered service call result:', {
      count: filteredAppointments.length,
      dateRange: { start: startOfWeek, end: endOfWeek }
    })

    return {
      success: true,
      totalAppointments: appointments.length,
      filteredAppointments: filteredAppointments.length
    }
  } catch (error) {
    console.error('❌ Appointments hook debug failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

// Auto-run basic tests in development
if (process.env.NODE_ENV === 'development') {
  // Run tests after a short delay to ensure everything is loaded
  setTimeout(() => {
    logEnvironmentInfo()
    testDatabaseConnection().then(result => {
      console.log('🔍 Database test result:', result)
    })
  }, 2000)
}
