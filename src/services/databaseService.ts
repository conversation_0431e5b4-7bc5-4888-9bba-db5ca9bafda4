import { supabase } from '@/lib/supabase'
import { testDatabaseConnectivity, parseSupabaseError, withRetry } from '@/utils/databaseHelpers'

export class DatabaseService {
  /**
   * Get appointments with metrics using CTE for better performance
   */
  static async getAppointmentsWithMetrics(
    companyId?: string,
    dateRange?: { start: Date; end: Date }
  ) {
    try {
      console.log('Getting appointments with metrics for company:', companyId)
      
      const query = `
        WITH filtered_appointments AS (
          SELECT *
          FROM appointments
          WHERE 
            ($1::text IS NULL OR company_id = $1)
            AND ($2::timestamptz IS NULL OR booked_for >= $2)
            AND ($3::timestamptz IS NULL OR booked_for <= $3)
        ),
        appointment_metrics AS (
          SELECT 
            COUNT(*) as total_appointments,
            COUNT(*) FILTER (WHERE confirmation_disposition = 'Sat') as total_sits,
            COUNT(*) FILTER (WHERE confirmation_disposition = 'Closed') as total_closes,
            COUNT(*) FILTER (WHERE confirmation_disposition = 'No Show') as no_shows,
            COUNT(*) FILTER (WHERE confirmation_disposition = 'Rescheduled') as rescheduled,
            COUNT(*) FILTER (WHERE confirmation_disposition = 'Not Interested') as not_interested,
            COUNT(*) FILTER (WHERE confirmation_disposition = 'Disqualified') as disqualified,
            ARRAY_AGG(DISTINCT closer_name) FILTER (WHERE closer_name IS NOT NULL) as unique_closers,
            ARRAY_AGG(DISTINCT setter_name) FILTER (WHERE setter_name IS NOT NULL) as unique_setters
          FROM filtered_appointments
        )
        SELECT 
          (SELECT json_agg(fa.*) FROM filtered_appointments fa) as appointments,
          (SELECT row_to_json(am.*) FROM appointment_metrics am) as metrics
      `
      
      const { data, error } = await supabase.rpc('execute_query', {
        query_text: query,
        params: [
          companyId || null,
          dateRange?.start?.toISOString() || null,
          dateRange?.end?.toISOString() || null
        ]
      })

      if (error) {
        console.error('CTE query error:', error)
        // Fallback to simple query
        return this.getFallbackAppointments(companyId, dateRange)
      }

      return {
        appointments: data?.[0]?.appointments || [],
        metrics: data?.[0]?.metrics || this.getDefaultMetrics()
      }
    } catch (error) {
      console.error('Error in getAppointmentsWithMetrics:', error)
      return this.getFallbackAppointments(companyId, dateRange)
    }
  }

  /**
   * Fallback method using simple queries
   */
  private static async getFallbackAppointments(
    companyId?: string,
    dateRange?: { start: Date; end: Date }
  ) {
    try {
      let query = supabase
        .from('appointments')
        .select('*')
        .order('booked_for', { ascending: false })

      if (companyId) {
        query = query.eq('company_id', companyId)
      }

      if (dateRange) {
        query = query
          .gte('booked_for', dateRange.start.toISOString())
          .lte('booked_for', dateRange.end.toISOString())
      }

      const { data: appointments, error } = await query

      if (error) {
        console.error('Fallback query error:', error)
        return {
          appointments: [],
          metrics: this.getDefaultMetrics()
        }
      }

      const metrics = this.calculateMetrics(appointments || [])
      
      return {
        appointments: appointments || [],
        metrics
      }
    } catch (error) {
      console.error('Error in fallback query:', error)
      return {
        appointments: [],
        metrics: this.getDefaultMetrics()
      }
    }
  }

  /**
   * Calculate metrics from appointments array
   */
  private static calculateMetrics(appointments: any[]) {
    const total = appointments.length
    const sits = appointments.filter(apt => apt.confirmation_disposition === 'Sat').length
    const closes = appointments.filter(apt => apt.confirmation_disposition === 'Closed').length
    const noShows = appointments.filter(apt => apt.confirmation_disposition === 'No Show').length
    const rescheduled = appointments.filter(apt => apt.confirmation_disposition === 'Rescheduled').length
    const notInterested = appointments.filter(apt => apt.confirmation_disposition === 'Not Interested').length
    const disqualified = appointments.filter(apt => apt.confirmation_disposition === 'Disqualified').length

    return {
      total_appointments: total,
      total_sits: sits,
      total_closes: closes,
      no_shows: noShows,
      rescheduled,
      not_interested: notInterested,
      disqualified,
      unique_closers: [...new Set(appointments.map(apt => apt.closer_name).filter(Boolean))],
      unique_setters: [...new Set(appointments.map(apt => apt.setter_name).filter(Boolean))]
    }
  }

  /**
   * Get default metrics structure
   */
  private static getDefaultMetrics() {
    return {
      total_appointments: 0,
      total_sits: 0,
      total_closes: 0,
      no_shows: 0,
      rescheduled: 0,
      not_interested: 0,
      disqualified: 0,
      unique_closers: [],
      unique_setters: []
    }
  }

  /**
   * Test database connection and permissions using enhanced helpers
   */
  static async testConnection() {
    try {
      console.log('Testing database connection with enhanced error handling...')

      // Use the enhanced connectivity test with retry mechanism
      const result = await withRetry(async () => {
        return await testDatabaseConnectivity()
      }, 2, 500)

      if (!result.success) {
        const errorInfo = parseSupabaseError(new Error(result.error || 'Unknown error'))
        return {
          success: false,
          error: errorInfo.userMessage,
          technicalError: errorInfo.technicalMessage,
          details: result.details,
          isPermissionError: errorInfo.isPermissionError,
          isConnectionError: errorInfo.isConnectionError
        }
      }

      console.log('Database connection test successful with counts:', result.data)

      return {
        success: true,
        message: 'All database connections working properly',
        tests: {
          connection: true,
          appointments: true,
          companies: true
        },
        counts: result.data
      }
    } catch (error) {
      console.error('Database test error:', error)
      const errorInfo = parseSupabaseError(error instanceof Error ? error : new Error('Unknown error'))

      return {
        success: false,
        error: errorInfo.userMessage,
        technicalError: errorInfo.technicalMessage,
        isPermissionError: errorInfo.isPermissionError,
        isConnectionError: errorInfo.isConnectionError,
        details: error
      }
    }
  }

  /**
   * Get user's accessible company IDs based on their role
   */
  static async getUserCompanyAccess(userId: string) {
    try {
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('role, company_id')
        .eq('id', userId)
        .single()

      if (error) {
        console.error('Error getting user company access:', error)
        return { companyIds: [], role: null }
      }

      if (profile.role === 'super_admin') {
        // Super admins can access all companies
        const { data: companies, error: companiesError } = await supabase
          .from('companies')
          .select('company_id')

        if (companiesError) {
          console.error('Error getting all companies:', companiesError)
          return { companyIds: [], role: profile.role }
        }

        return {
          companyIds: companies?.map(c => c.company_id) || [],
          role: profile.role
        }
      } else {
        // Regular users can only access their assigned company
        return {
          companyIds: profile.company_id ? [profile.company_id] : [],
          role: profile.role
        }
      }
    } catch (error) {
      console.error('Error in getUserCompanyAccess:', error)
      return { companyIds: [], role: null }
    }
  }
}
