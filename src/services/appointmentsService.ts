import { supabase } from '@/lib/supabase'
import { parseSupabaseError } from '@/utils/databaseHelpers'

export interface Appointment {
  id: string
  company_id: string
  name: string
  closer_name: string
  booked_for: string
  confirmation_disposition: 'Sat' | 'Rescheduled' | 'Not Interested' | 'Disqualified' | 'Follow-up' | 'Pending' | 'No Show' | 'Closed'
  note: string | null
  phone_number: string | null
  address: string | null
  setter_name: string | null
  setter_number: string | null
  email: string | null
  disposition_date: string | null
  site_survey: string | null
  m1_commission: number
  m2_commission: number
  contact_link: string | null
  recording_media_link: string | null
  credit_score: number | null
  roof_type: string | null
  existing_solar: boolean
  shading: string | null
  appointment_type: string | null
  confirmed: boolean
  contact_id: string | null
  created_at: string
  updated_at: string
}

export interface AppointmentFilters {
  dateRange: {
    start: Date
    end: Date
  }
  selectedClosers: string[]
  selectedSetters: string[]
}

export class AppointmentsService {
  static async getAppointments(filters?: AppointmentFilters): Promise<Appointment[]> {
    try {
      console.log('AppointmentsService.getAppointments called with filters:', filters)

      // Use a more robust query with CTE for complex filtering
      let query = supabase
        .from('appointments')
        .select(`
          *,
          companies!inner(company_id, company_name)
        `)
        .order('booked_for', { ascending: false })

      // Apply date range filter
      if (filters?.dateRange) {
        query = query
          .gte('booked_for', filters.dateRange.start.toISOString())
          .lte('booked_for', filters.dateRange.end.toISOString())
      }

      // Apply closer filter
      if (filters?.selectedClosers && filters.selectedClosers.length > 0) {
        query = query.in('closer_name', filters.selectedClosers)
      }

      // Apply setter filter
      if (filters?.selectedSetters && filters.selectedSetters.length > 0) {
        query = query.in('setter_name', filters.selectedSetters)
      }

      console.log('Executing appointments query...')
      const { data, error } = await query

      if (error) {
        console.error('Supabase error fetching appointments:', error)

        // Use enhanced error parsing
        const errorInfo = parseSupabaseError(error)
        console.error('Parsed error info:', errorInfo)

        // Handle specific error cases
        if (error.code === 'PGRST116') {
          console.log('No appointments found, returning empty array')
          return []
        }

        if (errorInfo.isPermissionError) {
          console.log('Permission denied or RLS policy blocking access, returning empty array')
          return []
        }

        if (error.message.includes('relation') || error.message.includes('does not exist')) {
          console.error('Database schema issue:', error.message)
          return []
        }

        // For other errors, still return empty array to prevent app crash
        console.error('Unexpected database error, returning empty array. User message:', errorInfo.userMessage)
        return []
      }

      console.log('Appointments query successful, returned:', data?.length || 0, 'appointments')

      // Transform data to remove nested company object if present
      const transformedData = data?.map(appointment => {
        if (appointment.companies) {
          // Remove the nested companies object since we already have company_id
          const { companies, ...cleanAppointment } = appointment
          return cleanAppointment
        }
        return appointment
      }) || []

      return transformedData
    } catch (error) {
      console.error('Error in getAppointments:', error)
      // Return empty array instead of throwing to prevent infinite loading
      return []
    }
  }

  static async createAppointment(appointment: Omit<Appointment, 'id' | 'created_at' | 'updated_at'>): Promise<Appointment> {
    try {
      const { data, error } = await supabase
        .from('appointments')
        .insert(appointment)
        .select()
        .single()

      if (error) {
        console.error('Error creating appointment:', error)
        throw error
      }

      return data
    } catch (error) {
      console.error('Error in createAppointment:', error)
      throw error
    }
  }

  static async updateAppointment(id: string, updates: Partial<Appointment>): Promise<Appointment> {
    try {
      const { data, error } = await supabase
        .from('appointments')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      if (error) {
        console.error('Error updating appointment:', error)
        throw error
      }

      return data
    } catch (error) {
      console.error('Error in updateAppointment:', error)
      throw error
    }
  }

  static async deleteAppointment(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('appointments')
        .delete()
        .eq('id', id)

      if (error) {
        console.error('Error deleting appointment:', error)
        throw error
      }
    } catch (error) {
      console.error('Error in deleteAppointment:', error)
      throw error
    }
  }

  static async getUniqueClosers(): Promise<string[]> {
    try {
      const { data, error } = await supabase
        .from('appointments')
        .select('closer_name')
        .not('closer_name', 'is', null)

      if (error) {
        console.error('Error fetching closers:', error)
        throw error
      }

      const uniqueClosers = [...new Set(data?.map(item => item.closer_name) || [])]
      return uniqueClosers.sort()
    } catch (error) {
      console.error('Error in getUniqueClosers:', error)
      throw error
    }
  }

  static async getUniqueSetters(): Promise<string[]> {
    try {
      const { data, error } = await supabase
        .from('appointments')
        .select('setter_name')
        .not('setter_name', 'is', null)

      if (error) {
        console.error('Error fetching setters:', error)
        throw error
      }

      const uniqueSetters = [...new Set(data?.map(item => item.setter_name) || [])]
      return uniqueSetters.sort()
    } catch (error) {
      console.error('Error in getUniqueSetters:', error)
      throw error
    }
  }

  // Helper method to seed some sample data for testing
  static async seedSampleData(companyId: string): Promise<void> {
    const sampleAppointments = [
      {
        company_id: companyId,
        name: 'John Smith',
        closer_name: 'Sarah Johnson',
        booked_for: new Date().toISOString(),
        confirmation_disposition: 'Sat' as const,
        note: 'Interested in solar installation',
        phone_number: '(*************',
        address: '123 Main St, Anytown, ST 12345',
        setter_name: 'Alex Parker',
        setter_number: '(*************',
        email: '<EMAIL>',
        disposition_date: new Date().toISOString().split('T')[0],
        site_survey: 'Completed',
        m1_commission: 250,
        m2_commission: 150,
        contact_link: 'https://crm.example.com/contact/1',
        recording_media_link: 'https://recordings.example.com/1',
        credit_score: 720,
        roof_type: 'Shingles',
        existing_solar: false,
        shading: 'No',
        appointment_type: 'In-Person',
        confirmed: true,
        contact_id: 'CONTACT_000001'
      },
      {
        company_id: companyId,
        name: 'Jane Doe',
        closer_name: 'Mike Chen',
        booked_for: new Date(Date.now() - 86400000).toISOString(), // Yesterday
        confirmation_disposition: 'Pending' as const,
        note: 'Follow up needed',
        phone_number: '(*************',
        address: '456 Oak Ave, Somewhere, ST 67890',
        setter_name: 'Lisa Brown',
        setter_number: '(*************',
        email: '<EMAIL>',
        disposition_date: null,
        site_survey: 'Scheduled',
        m1_commission: 300,
        m2_commission: 200,
        contact_link: 'https://crm.example.com/contact/2',
        recording_media_link: null,
        credit_score: 680,
        roof_type: 'Meadows',
        existing_solar: false,
        shading: 'Yes',
        appointment_type: 'Virtual',
        confirmed: false,
        contact_id: 'CONTACT_000002'
      }
    ]

    try {
      const { error } = await supabase
        .from('appointments')
        .insert(sampleAppointments)

      if (error) {
        console.error('Error seeding sample data:', error)
        throw error
      }

      console.log('Sample data seeded successfully')
    } catch (error) {
      console.error('Error in seedSampleData:', error)
      throw error
    }
  }
}
