import { ReactNode } from 'react'
import { Navigate } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import { Loader2 } from 'lucide-react'

interface ProtectedRouteProps {
  children: ReactNode
  requiredRole?: 'super_admin' | 'user'
  redirectTo?: string
}

export const ProtectedRoute = ({ 
  children, 
  requiredRole,
  redirectTo = '/login' 
}: ProtectedRouteProps) => {
  const { user, profile, loading } = useAuth()

  if (loading) {
    return (
      <div className="min-h-screen bg-dashboard-bg flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  if (!user) {
    return <Navigate to={redirectTo} replace />
  }

  if (requiredRole && profile?.role !== requiredRole) {
    // If user doesn't have required role, redirect to unauthorized page or dashboard
    return <Navigate to="/" replace />
  }

  return <>{children}</>
}

// Higher-order component for role-based access
export const withRoleProtection = (
  Component: React.ComponentType,
  requiredRole?: 'super_admin' | 'user'
) => {
  return (props: any) => (
    <ProtectedRoute requiredRole={requiredRole}>
      <Component {...props} />
    </ProtectedRoute>
  )
}
