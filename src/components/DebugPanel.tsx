import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { ChevronDown, ChevronRight, Bug, RefreshCw, Database } from 'lucide-react'
import { DatabaseService } from '@/services/databaseService'

export const DebugPanel = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [dbTest, setDbTest] = useState<any>(null)
  const [testing, setTesting] = useState(false)
  const { user, profile, session, loading, refreshProfile } = useAuth()

  const runDatabaseTest = async () => {
    setTesting(true)
    try {
      const result = await DatabaseService.testConnection()
      setDbTest(result)
    } catch (error) {
      setDbTest({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    } finally {
      setTesting(false)
    }
  }

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <Button variant="outline" size="sm" className="bg-background">
            <Bug className="mr-2 h-4 w-4" />
            Debug
            {isOpen ? (
              <ChevronDown className="ml-2 h-4 w-4" />
            ) : (
              <ChevronRight className="ml-2 h-4 w-4" />
            )}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <Card className="mt-2 w-80 max-h-96 overflow-auto">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm">Debug Information</CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={refreshProfile}
                  className="h-6 w-6 p-0"
                >
                  <RefreshCw className="h-3 w-3" />
                </Button>
              </div>
              <CardDescription className="text-xs">
                Development debugging panel
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3 text-xs">
              {/* Auth Loading State */}
              <div>
                <p className="font-medium mb-1">Auth Loading:</p>
                <Badge variant={loading ? "destructive" : "secondary"}>
                  {loading ? "Loading..." : "Loaded"}
                </Badge>
              </div>

              {/* User State */}
              <div>
                <p className="font-medium mb-1">User:</p>
                {user ? (
                  <div className="bg-muted p-2 rounded text-xs">
                    <p><strong>ID:</strong> {user.id}</p>
                    <p><strong>Email:</strong> {user.email}</p>
                    <p><strong>Created:</strong> {new Date(user.created_at).toLocaleString()}</p>
                  </div>
                ) : (
                  <Badge variant="outline">No User</Badge>
                )}
              </div>

              {/* Profile State */}
              <div>
                <p className="font-medium mb-1">Profile:</p>
                {profile ? (
                  <div className="bg-muted p-2 rounded text-xs">
                    <p><strong>ID:</strong> {profile.id}</p>
                    <p><strong>Email:</strong> {profile.email}</p>
                    <p><strong>Role:</strong> {profile.role}</p>
                    <p><strong>Company:</strong> {profile.company_id || 'None'}</p>
                  </div>
                ) : (
                  <Badge variant="outline">No Profile</Badge>
                )}
              </div>

              {/* Session State */}
              <div>
                <p className="font-medium mb-1">Session:</p>
                {session ? (
                  <div className="bg-muted p-2 rounded text-xs">
                    <p><strong>Access Token:</strong> {session.access_token.substring(0, 20)}...</p>
                    <p><strong>Expires:</strong> {new Date(session.expires_at! * 1000).toLocaleString()}</p>
                  </div>
                ) : (
                  <Badge variant="outline">No Session</Badge>
                )}
              </div>

              {/* Database Test */}
              <div>
                <div className="flex items-center justify-between mb-1">
                  <p className="font-medium">Database:</p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={runDatabaseTest}
                    disabled={testing}
                    className="h-6 text-xs"
                  >
                    <Database className="h-3 w-3 mr-1" />
                    {testing ? 'Testing...' : 'Test'}
                  </Button>
                </div>
                {dbTest && (
                  <div className="bg-muted p-2 rounded text-xs">
                    <p><strong>Status:</strong>
                      <Badge variant={dbTest.success ? "default" : "destructive"} className="ml-1 text-xs">
                        {dbTest.success ? "Connected" : "Failed"}
                      </Badge>
                    </p>
                    {dbTest.tests && (
                      <>
                        <p><strong>Appointments:</strong>
                          <Badge variant={dbTest.tests.appointments ? "default" : "destructive"} className="ml-1 text-xs">
                            {dbTest.tests.appointments ? "OK" : "Failed"}
                          </Badge>
                        </p>
                        <p><strong>Companies:</strong>
                          <Badge variant={dbTest.tests.companies ? "default" : "destructive"} className="ml-1 text-xs">
                            {dbTest.tests.companies ? "OK" : "Failed"}
                          </Badge>
                        </p>
                      </>
                    )}
                    {dbTest.error && (
                      <p className="text-destructive mt-1"><strong>Error:</strong> {dbTest.error}</p>
                    )}
                  </div>
                )}
              </div>

              {/* Environment */}
              <div>
                <p className="font-medium mb-1">Environment:</p>
                <div className="bg-muted p-2 rounded text-xs">
                  <p><strong>Supabase URL:</strong> {import.meta.env.VITE_SUPABASE_URL ? 'Set' : 'Missing'}</p>
                  <p><strong>Supabase Key:</strong> {import.meta.env.VITE_SUPABASE_ANON_KEY ? 'Set' : 'Missing'}</p>
                </div>
              </div>

              {/* Actions */}
              <div className="pt-2 border-t">
                <p className="font-medium mb-2">Actions:</p>
                <div className="space-y-1">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full text-xs"
                    onClick={() => {
                      console.log('=== DEBUG INFO ===')
                      console.log('User:', user)
                      console.log('Profile:', profile)
                      console.log('Session:', session)
                      console.log('Loading:', loading)
                      console.log('Environment:', {
                        supabaseUrl: import.meta.env.VITE_SUPABASE_URL,
                        hasAnonKey: !!import.meta.env.VITE_SUPABASE_ANON_KEY
                      })
                    }}
                  >
                    Log to Console
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full text-xs"
                    onClick={() => window.location.reload()}
                  >
                    Reload Page
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </CollapsibleContent>
      </Collapsible>
    </div>
  )
}
