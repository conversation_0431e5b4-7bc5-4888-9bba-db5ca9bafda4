import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { LogOut, Settings, User, Building2, Users } from 'lucide-react'
import { Link } from 'react-router-dom'

export const Navigation = () => {
  const { user, profile, signOut } = useAuth()

  if (!user || !profile) {
    return null
  }

  const getInitials = (email: string) => {
    return email.split('@')[0].slice(0, 2).toUpperCase()
  }

  const getRoleBadgeVariant = (role: string) => {
    return role === 'super_admin' ? 'destructive' : 'secondary'
  }

  return (
    <nav className="border-b border-border bg-card">
      <div className="container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link to="/" className="text-xl font-bold text-foreground">
              Clients Dashboard
            </Link>
            {profile.role === 'super_admin' && (
              <div className="flex items-center space-x-2">
                <Link to="/admin/companies">
                  <Button variant="ghost" size="sm">
                    <Building2 className="mr-2 h-4 w-4" />
                    Companies
                  </Button>
                </Link>
                <Link to="/admin/users">
                  <Button variant="ghost" size="sm">
                    <Users className="mr-2 h-4 w-4" />
                    Users
                  </Button>
                </Link>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-4">
            {profile.company_id && (
              <Badge variant="outline" className="hidden sm:inline-flex">
                {profile.company_id}
              </Badge>
            )}
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback>
                      {getInitials(profile.email)}
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {profile.email}
                    </p>
                    <div className="flex items-center space-x-2">
                      <Badge 
                        variant={getRoleBadgeVariant(profile.role)}
                        className="text-xs"
                      >
                        {profile.role.replace('_', ' ')}
                      </Badge>
                      {profile.company_id && (
                        <span className="text-xs text-muted-foreground">
                          {profile.company_id}
                        </span>
                      )}
                    </div>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <User className="mr-2 h-4 w-4" />
                  <span>Profile</span>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Settings</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={signOut}>
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Log out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </nav>
  )
}
