import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { CheckCircle, XCircle, AlertTriangle, RefreshCw, Database } from 'lucide-react'
import { DatabaseService } from '@/services/databaseService'
import { AppointmentsService } from '@/services/appointmentsService'
import { testDatabaseConnectivity, safeCount, safeSelect } from '@/utils/databaseHelpers'

interface TestResult {
  name: string
  status: 'pending' | 'success' | 'error' | 'warning'
  message: string
  details?: any
  duration?: number
}

export const ErrorResolutionTest: React.FC = () => {
  const [results, setResults] = useState<TestResult[]>([])
  const [running, setRunning] = useState(false)
  const [overallStatus, setOverallStatus] = useState<'pending' | 'success' | 'error'>('pending')

  const addResult = (result: TestResult) => {
    setResults(prev => [...prev, result])
  }

  const runComprehensiveTests = async () => {
    setRunning(true)
    setResults([])
    setOverallStatus('pending')

    const startTime = Date.now()
    let allPassed = true

    try {
      // Test 1: Database Connectivity Helper
      const connectivityStart = Date.now()
      try {
        const connectivityResult = await testDatabaseConnectivity()
        const duration = Date.now() - connectivityStart
        
        if (connectivityResult.success) {
          addResult({
            name: 'Database Connectivity Helper',
            status: 'success',
            message: `All tables accessible. Counts: ${JSON.stringify(connectivityResult.data)}`,
            duration,
            details: connectivityResult
          })
        } else {
          allPassed = false
          addResult({
            name: 'Database Connectivity Helper',
            status: 'error',
            message: connectivityResult.error || 'Unknown error',
            duration,
            details: connectivityResult
          })
        }
      } catch (error) {
        allPassed = false
        addResult({
          name: 'Database Connectivity Helper',
          status: 'error',
          message: error instanceof Error ? error.message : 'Unknown error',
          duration: Date.now() - connectivityStart,
          details: error
        })
      }

      // Test 2: Safe Count Queries
      const countStart = Date.now()
      try {
        const profilesCount = await safeCount('profiles')
        const appointmentsCount = await safeCount('appointments')
        const companiesCount = await safeCount('companies')
        const duration = Date.now() - countStart

        if (profilesCount.success && appointmentsCount.success && companiesCount.success) {
          addResult({
            name: 'Safe Count Queries',
            status: 'success',
            message: `Profiles: ${profilesCount.data}, Appointments: ${appointmentsCount.data}, Companies: ${companiesCount.data}`,
            duration,
            details: { profilesCount, appointmentsCount, companiesCount }
          })
        } else {
          allPassed = false
          const errors = [
            !profilesCount.success && `Profiles: ${profilesCount.error}`,
            !appointmentsCount.success && `Appointments: ${appointmentsCount.error}`,
            !companiesCount.success && `Companies: ${companiesCount.error}`
          ].filter(Boolean)
          
          addResult({
            name: 'Safe Count Queries',
            status: 'error',
            message: `Errors: ${errors.join(', ')}`,
            duration,
            details: { profilesCount, appointmentsCount, companiesCount }
          })
        }
      } catch (error) {
        allPassed = false
        addResult({
          name: 'Safe Count Queries',
          status: 'error',
          message: error instanceof Error ? error.message : 'Unknown error',
          duration: Date.now() - countStart,
          details: error
        })
      }

      // Test 3: DatabaseService.testConnection
      const dbServiceStart = Date.now()
      try {
        const dbResult = await DatabaseService.testConnection()
        const duration = Date.now() - dbServiceStart

        if (dbResult.success) {
          addResult({
            name: 'DatabaseService.testConnection',
            status: 'success',
            message: dbResult.message || 'Connection successful',
            duration,
            details: dbResult
          })
        } else {
          allPassed = false
          addResult({
            name: 'DatabaseService.testConnection',
            status: 'error',
            message: dbResult.error || 'Unknown error',
            duration,
            details: dbResult
          })
        }
      } catch (error) {
        allPassed = false
        addResult({
          name: 'DatabaseService.testConnection',
          status: 'error',
          message: error instanceof Error ? error.message : 'Unknown error',
          duration: Date.now() - dbServiceStart,
          details: error
        })
      }

      // Test 4: AppointmentsService.getAppointments
      const appointmentsStart = Date.now()
      try {
        const appointments = await AppointmentsService.getAppointments()
        const duration = Date.now() - appointmentsStart

        addResult({
          name: 'AppointmentsService.getAppointments',
          status: 'success',
          message: `Retrieved ${appointments.length} appointments`,
          duration,
          details: { count: appointments.length, sample: appointments[0] }
        })
      } catch (error) {
        allPassed = false
        addResult({
          name: 'AppointmentsService.getAppointments',
          status: 'error',
          message: error instanceof Error ? error.message : 'Unknown error',
          duration: Date.now() - appointmentsStart,
          details: error
        })
      }

      // Test 5: Safe Select Queries
      const selectStart = Date.now()
      try {
        const profilesResult = await safeSelect('profiles', 'id, email, role', { limit: 1 })
        const appointmentsResult = await safeSelect('appointments', 'id, name, confirmation_disposition', { limit: 3 })
        const duration = Date.now() - selectStart

        if (profilesResult.success && appointmentsResult.success) {
          addResult({
            name: 'Safe Select Queries',
            status: 'success',
            message: `Retrieved ${profilesResult.data?.length} profiles, ${appointmentsResult.data?.length} appointments`,
            duration,
            details: { profilesResult, appointmentsResult }
          })
        } else {
          allPassed = false
          const errors = [
            !profilesResult.success && `Profiles: ${profilesResult.error}`,
            !appointmentsResult.success && `Appointments: ${appointmentsResult.error}`
          ].filter(Boolean)
          
          addResult({
            name: 'Safe Select Queries',
            status: 'error',
            message: `Errors: ${errors.join(', ')}`,
            duration,
            details: { profilesResult, appointmentsResult }
          })
        }
      } catch (error) {
        allPassed = false
        addResult({
          name: 'Safe Select Queries',
          status: 'error',
          message: error instanceof Error ? error.message : 'Unknown error',
          duration: Date.now() - selectStart,
          details: error
        })
      }

      const totalDuration = Date.now() - startTime
      setOverallStatus(allPassed ? 'success' : 'error')

      addResult({
        name: 'Overall Test Summary',
        status: allPassed ? 'success' : 'error',
        message: `${allPassed ? 'All tests passed!' : 'Some tests failed.'} Total duration: ${totalDuration}ms`,
        duration: totalDuration
      })

    } catch (error) {
      setOverallStatus('error')
      addResult({
        name: 'Test Runner Error',
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        details: error
      })
    } finally {
      setRunning(false)
    }
  }

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      default:
        return <RefreshCw className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusVariant = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return 'default'
      case 'error':
        return 'destructive'
      case 'warning':
        return 'secondary'
      default:
        return 'outline'
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          HTTP 500 Error Resolution Test
        </CardTitle>
        <div className="flex items-center gap-4">
          <Button onClick={runComprehensiveTests} disabled={running}>
            {running ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Running Tests...
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                Run Comprehensive Tests
              </>
            )}
          </Button>
          
          {overallStatus !== 'pending' && (
            <Badge variant={overallStatus === 'success' ? 'default' : 'destructive'}>
              {overallStatus === 'success' ? 'All Tests Passed' : 'Some Tests Failed'}
            </Badge>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {results.length === 0 && !running && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Click "Run Comprehensive Tests" to verify that the HTTP 500 errors have been resolved.
            </AlertDescription>
          </Alert>
        )}

        {results.map((result, index) => (
          <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
            <div className="flex items-center gap-3 flex-1">
              {getStatusIcon(result.status)}
              <div className="flex-1">
                <p className="font-medium">{result.name}</p>
                <p className="text-sm text-muted-foreground">{result.message}</p>
                {result.duration && (
                  <p className="text-xs text-muted-foreground">Duration: {result.duration}ms</p>
                )}
              </div>
            </div>
            <Badge variant={getStatusVariant(result.status)}>
              {result.status}
            </Badge>
          </div>
        ))}

        {overallStatus === 'success' && results.length > 0 && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Success!</strong> All database queries are working correctly. The HTTP 500 errors should be resolved.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  )
}
