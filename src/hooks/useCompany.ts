import { useState, useEffect, useRef } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase'

interface CompanyInfo {
  id: string
  company_id: string
  company_name: string
}

export const useCompany = () => {
  const { profile } = useAuth()
  const [company, setCompany] = useState<CompanyInfo | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const isMountedRef = useRef(true)

  useEffect(() => {
    const fetchCompany = async () => {
      if (!profile?.company_id) {
        if (isMountedRef.current) {
          setLoading(false)
        }
        return
      }

      try {
        if (isMountedRef.current) {
          setLoading(true)
          setError(null)
        }

        const { data, error: fetchError } = await supabase
          .from('companies')
          .select('id, company_id, company_name')
          .eq('company_id', profile.company_id)
          .single()

        if (!isMountedRef.current) return

        if (fetchError) {
          if (process.env.NODE_ENV === 'development') {
            console.error('Error fetching company:', fetchError)
          }
          setError('Failed to fetch company information')
          return
        }

        setCompany(data)
      } catch (err) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Unexpected error fetching company:', err)
        }
        if (isMountedRef.current) {
          setError('An unexpected error occurred')
        }
      } finally {
        if (isMountedRef.current) {
          setLoading(false)
        }
      }
    }

    fetchCompany()
  }, [profile?.company_id])

  useEffect(() => {
    return () => {
      isMountedRef.current = false
    }
  }, [])

  return {
    company,
    loading,
    error,
    companyName: company?.company_name || 'Unknown Company'
  }
}
