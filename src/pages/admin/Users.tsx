import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { supabase, Profile, Company } from '@/lib/supabase'
import { Navigation } from '@/components/layout/Navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { toast } from '@/components/ui/sonner'
import { Plus, Edit, Loader2 } from 'lucide-react'
import { format } from 'date-fns'

const Users = () => {
  const { profile } = useAuth()
  const [users, setUsers] = useState<Profile[]>([])
  const [companies, setCompanies] = useState<Company[]>([])
  const [loading, setLoading] = useState(true)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingUser, setEditingUser] = useState<Profile | null>(null)
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    role: 'user' as 'super_admin' | 'user',
    company_id: 'no-company'
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Redirect if not super admin
  if (profile?.role !== 'super_admin') {
    return (
      <div className="min-h-screen bg-dashboard-bg">
        <Navigation />
        <div className="container mx-auto px-6 py-6">
          <Card>
            <CardContent className="pt-6">
              <p className="text-center text-muted-foreground">
                Access denied. This page is only available to super administrators.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  const fetchUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) {
        toast.error('Failed to fetch users')
        console.error('Error fetching users:', error)
        return
      }

      setUsers(data || [])
    } catch (error) {
      toast.error('An unexpected error occurred')
      console.error('Error:', error)
    }
  }

  const fetchCompanies = async () => {
    try {
      const { data, error } = await supabase
        .from('companies')
        .select('*')
        .order('company_name', { ascending: true })

      if (error) {
        toast.error('Failed to fetch companies')
        console.error('Error fetching companies:', error)
        return
      }

      setCompanies(data || [])
    } catch (error) {
      toast.error('An unexpected error occurred')
      console.error('Error:', error)
    }
  }

  useEffect(() => {
    const loadData = async () => {
      setLoading(true)
      await Promise.all([fetchUsers(), fetchCompanies()])
      setLoading(false)
    }
    loadData()
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      if (editingUser) {
        // Update existing user profile
        const { error } = await supabase
          .from('profiles')
          .update({
            role: formData.role,
            company_id: formData.company_id === 'no-company' ? null : formData.company_id
          })
          .eq('id', editingUser.id)

        if (error) {
          toast.error('Failed to update user')
          console.error('Error updating user:', error)
          return
        }

        toast.success('User updated successfully')
      } else {
        // Create new user using Supabase Auth
        const { data: authData, error: authError } = await supabase.auth.signUp({
          email: formData.email,
          password: formData.password,
          options: {
            emailRedirectTo: undefined, // Disable email confirmation
            data: {
              role: formData.role,
              company_id: formData.company_id === 'no-company' ? null : formData.company_id
            }
          }
        })

        if (authError) {
          toast.error(authError.message)
          console.error('Error creating user:', authError)
          return
        }

        if (authData.user) {
          // Update the profile with role and company
          const { error: profileError } = await supabase
            .from('profiles')
            .update({
              role: formData.role,
              company_id: formData.company_id === 'no-company' ? null : formData.company_id
            })
            .eq('id', authData.user.id)

          if (profileError) {
            console.error('Error updating profile:', profileError)
          }
        }

        toast.success('User created successfully')
      }

      // Reset form and close dialog
      setFormData({ email: '', password: '', role: 'user', company_id: 'no-company' })
      setEditingUser(null)
      setIsDialogOpen(false)
      fetchUsers()
    } catch (error) {
      toast.error('An unexpected error occurred')
      console.error('Error:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleEdit = (user: Profile) => {
    setEditingUser(user)
    setFormData({
      email: user.email,
      password: '', // Don't show existing password
      role: user.role,
      company_id: user.company_id || 'no-company'
    })
    setIsDialogOpen(true)
  }

  const resetForm = () => {
    setFormData({ email: '', password: '', role: 'user', company_id: 'no-company' })
    setEditingUser(null)
    setIsDialogOpen(false)
  }

  const getRoleBadgeVariant = (role: string) => {
    return role === 'super_admin' ? 'destructive' : 'secondary'
  }

  const getCompanyName = (companyId: string | null) => {
    if (!companyId) return 'No Company'
    const company = companies.find(c => c.company_id === companyId)
    return company ? company.company_name : companyId
  }

  return (
    <div className="min-h-screen bg-dashboard-bg">
      <Navigation />
      <div className="container mx-auto px-6 py-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Users</h1>
            <p className="text-muted-foreground">Manage users in the system</p>
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={resetForm}>
                <Plus className="mr-2 h-4 w-4" />
                Add User
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>
                  {editingUser ? 'Edit User' : 'Add New User'}
                </DialogTitle>
                <DialogDescription>
                  {editingUser 
                    ? 'Update the user information below.' 
                    : 'Enter the details for the new user. No email verification is required.'
                  }
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                    placeholder="Enter email address"
                    required
                    disabled={isSubmitting || !!editingUser}
                  />
                </div>
                {!editingUser && (
                  <div className="space-y-2">
                    <Label htmlFor="password">Password</Label>
                    <Input
                      id="password"
                      type="password"
                      value={formData.password}
                      onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                      placeholder="Enter password"
                      required
                      disabled={isSubmitting}
                    />
                  </div>
                )}
                <div className="space-y-2">
                  <Label htmlFor="role">Role</Label>
                  <Select
                    value={formData.role}
                    onValueChange={(value: 'super_admin' | 'user') => 
                      setFormData(prev => ({ ...prev, role: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="user">User</SelectItem>
                      <SelectItem value="super_admin">Super Admin</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="company_id">Company</Label>
                  <Select
                    value={formData.company_id}
                    onValueChange={(value) => 
                      setFormData(prev => ({ ...prev, company_id: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select company (optional)" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="no-company">No Company</SelectItem>
                      {companies.map((company) => (
                        <SelectItem key={company.id} value={company.company_id}>
                          {company.company_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex justify-end space-x-2">
                  <Button type="button" variant="outline" onClick={resetForm} disabled={isSubmitting}>
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        {editingUser ? 'Updating...' : 'Creating...'}
                      </>
                    ) : (
                      editingUser ? 'Update User' : 'Create User'
                    )}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>All Users</CardTitle>
            <CardDescription>
              {users.length} users in the system
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
              </div>
            ) : users.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No users found</p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Email</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Company</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">{user.email}</TableCell>
                      <TableCell>
                        <Badge variant={getRoleBadgeVariant(user.role)}>
                          {user.role.replace('_', ' ')}
                        </Badge>
                      </TableCell>
                      <TableCell>{getCompanyName(user.company_id)}</TableCell>
                      <TableCell>{format(new Date(user.created_at), 'MMM dd, yyyy')}</TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(user)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default Users
