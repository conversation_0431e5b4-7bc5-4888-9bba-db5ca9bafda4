@tailwind base;
@tailwind components;
@tailwind utilities;

/* Professional Analytics Dashboard Design System
   Inspired by modern dark-themed dashboards
   All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Dashboard Dark Theme */
    --dashboard-bg: 210 20% 6%;          /* Deep dark blue background */
    --dashboard-surface: 215 25% 10%;     /* Card backgrounds */
    --dashboard-surface-hover: 215 25% 12%; /* Card hover state */
    
    /* Professional Color Palette */
    --background: 210 20% 6%;
    --foreground: 210 40% 98%;
    
    --card: 215 25% 10%;
    --card-foreground: 210 40% 98%;
    
    --popover: 215 25% 10%;
    --popover-foreground: 210 40% 98%;
    
    /* Brand Colors */
    --primary: 160 84% 39%;               /* Professional teal */
    --primary-foreground: 210 40% 98%;
    
    --secondary: 215 25% 15%;             /* Darker surface */
    --secondary-foreground: 210 40% 98%;
    
    /* Status Colors */
    --success: 142 76% 36%;               /* Green for positive metrics */
    --success-foreground: 210 40% 98%;
    
    --warning: 38 92% 50%;                /* Amber for attention */
    --warning-foreground: 210 40% 98%;
    
    --info: 217 91% 60%;                  /* Blue for neutral */
    --info-foreground: 210 40% 98%;
    
    --muted: 215 15% 25%;
    --muted-foreground: 215 20% 65%;
    
    --accent: 215 25% 15%;
    --accent-foreground: 210 40% 98%;
    
    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;
    
    --border: 215 25% 20%;
    --input: 215 25% 15%;
    --ring: 160 84% 39%;
    
    --radius: 0.75rem;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(160 84% 45%));
    --gradient-success: linear-gradient(135deg, hsl(var(--success)), hsl(142 76% 42%));
    --gradient-warning: linear-gradient(135deg, hsl(var(--warning)), hsl(38 92% 56%));
    --gradient-info: linear-gradient(135deg, hsl(var(--info)), hsl(217 91% 66%));
    
    /* Shadows */
    --shadow-card: 0 4px 6px -1px hsl(210 20% 4% / 0.4), 0 2px 4px -1px hsl(210 20% 4% / 0.3);
    --shadow-card-hover: 0 10px 15px -3px hsl(210 20% 4% / 0.4), 0 4px 6px -2px hsl(210 20% 4% / 0.3);
    
    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}